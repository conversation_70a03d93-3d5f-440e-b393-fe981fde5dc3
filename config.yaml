datasets:
  data_dir: 'data/synthetic'
  topics: 'therapy'
  therapy_source_dir: 'data/source/HOPE_WSDM_2022/conversations'
  legal_source_dir: 'data/source/legal'
  writing_source_dir: 'data/source/creative_writing/human_wp_stories_cleaned.json'
  coding_source_dir: 'data/source/leetcode_hard/java_solutions.txt'
  email_source_dir: 'data/source/email_records/filtered_files_sager-e.txt'
  persona_file: 'data/source/Persona_Hub_200000.jsonl'
  random_questions_file: 'data/random_questions.txt'
  random_code_questions_file: 'data/random_code_questions.txt'
  random_contexts_file: 'data/irrelevant_contexts.json'
models:
  llm_model: 'gpt-4o'  # 'gpt-4-turbo', 'gpt-4o'
  gemini_credential_path: 'google-cloud-sdk/google_gemini_credential.json'
inference:
  verbose: False
  print_every: 50
  num_personas: 1
  num_samples_per_context: 1
  start_persona_idx: 0
  start_sample_idx: 0
  output_dir: 'data/output/'
  save_output_response: True
  output_file_name: 'conversation'