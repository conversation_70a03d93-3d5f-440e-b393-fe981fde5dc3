"""
Enhanced PersonaMem Inference with OpenRouter Integration and Function Calling
"""

import os
import json
import argparse
import yaml
import re
from typing import List, Dict, Any, Optional

from prepare_blocks import *
from openai import OpenAI


class OpenRouterClient:
    """Enhanced OpenRouter client with function calling support"""
    
    def __init__(self, api_key: str, base_url: str = "https://openrouter.ai/api/v1"):
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            default_headers={
                "HTTP-Referer": "https://foxychat.ai",
                "X-Title": "<PERSON><PERSON> Chat"
            }
        )
        
        # Model mapping for OpenRouter
        self.model_mapping = {
            "gpt-4o": "openai/gpt-4o",
            "gpt-4o-mini": "openai/gpt-4o-mini",
            "o1": "openai/o1",
            "o1-mini": "openai/o1-mini",
            "o3-mini": "openai/o3-mini",
            "claude-3-5-sonnet": "anthropic/claude-3.5-sonnet",
            "claude-3-haiku": "anthropic/claude-3-haiku",
            "gemini-2.0-flash": "google/gemini-2.0-flash-exp",
            "gemini-1.5-pro": "google/gemini-1.5-pro",
            "llama-3.1-405b": "meta-llama/llama-3.1-405b-instruct",
            "deepseek-r1": "deepseek/deepseek-r1",
        }
        
        # Function tools for PersonaMem
        self.personamem_tools = [
            {
                "type": "function",
                "function": {
                    "name": "analyze_user_preference",
                    "description": "Analyze and extract user preferences from conversation context",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "preference_type": {
                                "type": "string",
                                "enum": ["likes", "dislikes", "neutral", "changed"],
                                "description": "Type of preference identified"
                            },
                            "preference_item": {
                                "type": "string",
                                "description": "The specific item, activity, or topic of preference"
                            },
                            "confidence_score": {
                                "type": "number",
                                "minimum": 0,
                                "maximum": 1,
                                "description": "Confidence in the preference analysis (0-1)"
                            },
                            "context_evidence": {
                                "type": "string",
                                "description": "Direct quote or evidence from context supporting this preference"
                            },
                            "temporal_context": {
                                "type": "string",
                                "description": "When this preference was expressed or changed"
                            }
                        },
                        "required": ["preference_type", "preference_item", "confidence_score", "context_evidence"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "track_preference_evolution",
                    "description": "Track how user preferences have evolved over multiple sessions",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "preference_changes": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "item": {"type": "string"},
                                        "original_preference": {"type": "string"},
                                        "updated_preference": {"type": "string"},
                                        "change_reason": {"type": "string"},
                                        "session_context": {"type": "string"}
                                    }
                                },
                                "description": "List of preference changes over time"
                            },
                            "consistency_score": {
                                "type": "number",
                                "minimum": 0,
                                "maximum": 1,
                                "description": "Overall consistency of user preferences"
                            }
                        },
                        "required": ["preference_changes", "consistency_score"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "generate_personalized_recommendation",
                    "description": "Generate personalized recommendations based on user profile and preferences",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "recommendations": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "item": {"type": "string"},
                                        "reasoning": {"type": "string"},
                                        "alignment_score": {"type": "number", "minimum": 0, "maximum": 1}
                                    }
                                },
                                "description": "List of personalized recommendations with reasoning"
                            },
                            "avoided_items": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Items explicitly avoided based on user dislikes"
                            },
                            "personalization_factors": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Key factors used for personalization"
                            }
                        },
                        "required": ["recommendations", "personalization_factors"]
                    }
                }
            }
        ]
    
    def query_llm(self, question: str, all_options: str, context: Optional[List[Dict]] = None,
                  instructions: Optional[str] = None, model: str = "gpt-4o",
                  use_functions: bool = True, temperature: float = 0.1,
                  max_tokens: int = 2048, verbose: bool = False) -> Dict[str, Any]:
        """Enhanced query with function calling support"""
        
        if instructions is None:
            instructions = "Find the most appropriate model response and give your final answer (a), (b), (c), or (d) after the special token <final_answer>."
        
        # Prepare messages
        if context:
            messages = context + [{"role": "user", "content": f"{question}\n\n{instructions}\n\n{all_options}"}]
        else:
            messages = [{"role": "user", "content": f"{question}\n\n{instructions}\n\n{all_options}"}]
        
        # Map model name to OpenRouter format
        openrouter_model = self.model_mapping.get(model, model)
        
        try:
            # Prepare request parameters
            request_params = {
                "model": openrouter_model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }
            
            # Add function tools for supported models
            if use_functions and self._supports_functions(model):
                request_params["tools"] = self.personamem_tools
                request_params["tool_choice"] = "auto"
            
            # Make initial API call
            response = self.client.chat.completions.create(**request_params)
            
            # Process response
            result = {
                "content": response.choices[0].message.content,
                "model": response.model,
                "usage": response.usage.model_dump() if response.usage else None,
                "function_calls": [],
                "enhanced": False
            }
            
            # Handle function calls
            if response.choices[0].message.tool_calls:
                result["enhanced"] = True
                function_results = []
                
                for tool_call in response.choices[0].message.tool_calls:
                    func_name = tool_call.function.name
                    func_args = json.loads(tool_call.function.arguments)
                    
                    # Execute function
                    func_result = self._execute_personamem_function(func_name, func_args, context)
                    function_results.append({
                        "tool_call_id": tool_call.id,
                        "function_name": func_name,
                        "arguments": func_args,
                        "result": func_result
                    })
                
                result["function_calls"] = function_results
                
                # Make follow-up call with function results
                enhanced_messages = messages + [
                    {
                        "role": "assistant",
                        "content": result["content"],
                        "tool_calls": [
                            {
                                "id": fc["tool_call_id"],
                                "type": "function",
                                "function": {
                                    "name": fc["function_name"],
                                    "arguments": json.dumps(fc["arguments"])
                                }
                            }
                            for fc in function_results
                        ]
                    }
                ] + [
                    {
                        "role": "tool",
                        "tool_call_id": fc["tool_call_id"],
                        "content": json.dumps(fc["result"])
                    }
                    for fc in function_results
                ]
                
                # Final call with enhanced context
                final_response = self.client.chat.completions.create(
                    model=openrouter_model,
                    messages=enhanced_messages + [
                        {"role": "user", "content": "Based on the function analysis, provide your final answer with the format requested."}
                    ],
                    temperature=temperature,
                    max_tokens=max_tokens
                )
                
                result["content"] = final_response.choices[0].message.content
                if final_response.usage:
                    # Combine usage stats
                    if result["usage"]:
                        result["usage"]["prompt_tokens"] += final_response.usage.prompt_tokens
                        result["usage"]["completion_tokens"] += final_response.usage.completion_tokens
                        result["usage"]["total_tokens"] += final_response.usage.total_tokens
            
            if verbose:
                print(f"Model: {result['model']}")
                print(f"Enhanced: {result['enhanced']}")
                if result['usage']:
                    print(f"Tokens: {result['usage']['total_tokens']}")
                if result['function_calls']:
                    print(f"Function calls: {len(result['function_calls'])}")
            
            return result
            
        except Exception as e:
            print(f"OpenRouter API error: {e}")
            return {"content": None, "error": str(e), "enhanced": False}
    
    def _supports_functions(self, model: str) -> bool:
        """Check if model supports function calling"""
        function_capable_models = [
            "gpt-4o", "gpt-4o-mini", "claude-3-5-sonnet", 
            "gemini-1.5-pro", "gemini-2.0-flash"
        ]
        return model in function_capable_models
    
    def _execute_personamem_function(self, func_name: str, func_args: Dict, context: List[Dict]) -> Dict:
        """Execute PersonaMem-specific functions"""
        
        if func_name == "analyze_user_preference":
            return self._analyze_preference(func_args, context)
        elif func_name == "track_preference_evolution":
            return self._track_evolution(func_args, context)
        elif func_name == "generate_personalized_recommendation":
            return self._generate_recommendations(func_args, context)
        
        return {"status": "function_executed", "function": func_name}
    
    def _analyze_preference(self, args: Dict, context: List[Dict]) -> Dict:
        """Analyze user preferences from context"""
        context_text = " ".join([msg.get("content", "") for msg in context if msg.get("content")])
        
        preference_item = args.get("preference_item", "")
        preference_type = args.get("preference_type", "")
        
        # Simple analysis based on context
        analysis = {
            "preference_validated": True,
            "context_support": len([line for line in context_text.split('\n') if preference_item.lower() in line.lower()]),
            "confidence_validated": args.get("confidence_score", 0.5),
            "analysis_summary": f"Found {preference_type} preference for {preference_item}"
        }
        
        return analysis
    
    def _track_evolution(self, args: Dict, context: List[Dict]) -> Dict:
        """Track preference evolution over time"""
        changes = args.get("preference_changes", [])
        
        evolution_analysis = {
            "changes_tracked": len(changes),
            "evolution_pattern": "consistent" if args.get("consistency_score", 0) > 0.7 else "variable",
            "temporal_analysis": "preferences show evolution over multiple sessions"
        }
        
        return evolution_analysis
    
    def _generate_recommendations(self, args: Dict, context: List[Dict]) -> Dict:
        """Generate personalized recommendations"""
        recommendations = args.get("recommendations", [])
        factors = args.get("personalization_factors", [])
        
        recommendation_analysis = {
            "recommendations_count": len(recommendations),
            "personalization_depth": len(factors),
            "alignment_quality": "high" if len(factors) > 2 else "medium",
            "recommendation_summary": f"Generated {len(recommendations)} personalized recommendations"
        }
        
        return recommendation_analysis


class EnhancedEvaluation:
    """Enhanced evaluation class with OpenRouter and function calling"""
    
    def __init__(self, args, cmd_args):
        self.args = args
        self.cmd_args = cmd_args
        
        # Initialize OpenRouter client
        token_path = cmd_args.token_path
        with open(os.path.join(token_path, "openrouter_key.txt"), "r") as f:
            openrouter_key = f.read().strip()
        
        self.client = OpenRouterClient(api_key=openrouter_key)
        
        # Configuration
        self.use_functions = args.get('inference', {}).get('use_function_calling', True)
        self.function_models = args.get('inference', {}).get('function_calling_models', ['gpt-4o'])
    
    def query_llm(self, question: str, all_options: str, context: Optional[List[Dict]] = None,
                  instructions: Optional[str] = None, verbose: bool = False) -> str:
        """Query LLM with enhanced capabilities"""
        
        model = self.args['models']['llm_model']
        use_functions = self.use_functions and model in self.function_models
        
        result = self.client.query_llm(
            question=question,
            all_options=all_options,
            context=context,
            instructions=instructions,
            model=model,
            use_functions=use_functions,
            verbose=verbose
        )
        
        if result.get("error"):
            print(f"Error in query: {result['error']}")
            return ""
        
        # Log enhanced responses
        if result.get("enhanced") and verbose:
            print(f"Enhanced response with {len(result.get('function_calls', []))} function calls")
        
        return result.get("content", "")
    
    def extract_answer(self, predicted_answer: str, correct_answer: str) -> tuple:
        """Extract answer from response (same as original)"""
        def _extract_only_options(text):
            text = text.lower()
            in_parens = re.findall(r'\(([a-d])\)', text)
            if in_parens:
                return set(in_parens)
            else:
                return set(re.findall(r'\b([a-d])\b', text))

        correct = correct_answer.lower().strip("() ")
        
        # Clean predicted_answer
        full_response = predicted_answer
        predicted_answer = predicted_answer.strip()
        if "<final_answer>" in predicted_answer:
            predicted_answer = predicted_answer.split("<final_answer>")[-1].strip()
        if predicted_answer.endswith("</final_answer>"):
            predicted_answer = predicted_answer[:-len("</final_answer>")].strip()

        pred_options = _extract_only_options(predicted_answer)

        # First try the predicted_answer
        if pred_options == {correct}:
            return True, predicted_answer

        # Optionally fallback to model_response if provided
        response_options = _extract_only_options(full_response)
        if response_options == {correct}:
            return True, predicted_answer

        return False, predicted_answer


if __name__ == "__main__":
    # Load configuration
    try:
        with open('config.yaml', 'r') as file:
            args = yaml.safe_load(file)
    except Exception as e:
        print('Error reading the config file')
        exit(1)
    
    # Command-line arguments
    parser = argparse.ArgumentParser(description='Enhanced PersonaMem with OpenRouter')
    parser.add_argument('--model', type=str, default="gpt-4o", 
                       help='Model to use via OpenRouter')
    parser.add_argument('--step', type=str, default='evaluate', 
                       help='Step to run: evaluate')
    parser.add_argument('--token_path', type=str, default='api_tokens', 
                       help='Path to API tokens')
    parser.add_argument('--question_path', type=str, default='data/questions_128k.csv',
                       help='Path to questions CSV')
    parser.add_argument('--context_path', type=str, default='data/shared_contexts_128k.jsonl',
                       help='Path to contexts JSONL')
    parser.add_argument('--result_path', type=str, default='data/enhanced_results.csv',
                       help='Path to save results')
    parser.add_argument('--use_functions', action='store_true',
                       help='Enable function calling')
    parser.add_argument('--verbose', action='store_true',
                       help='Verbose output')
    
    cmd_args = parser.parse_args()
    args['models']['llm_model'] = cmd_args.model
    
    # Initialize enhanced evaluation
    llm = EnhancedEvaluation(args, cmd_args)
    
    print(f"Starting enhanced evaluation with OpenRouter")
    print(f"Model: {cmd_args.model}")
    print(f"Function calling: {cmd_args.use_functions}")
    
    # Run evaluation (implement your evaluation logic here)
    # This would be similar to the original run_evaluation function
    # but using the enhanced LLM client
