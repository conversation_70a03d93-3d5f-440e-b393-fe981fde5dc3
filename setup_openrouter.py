#!/usr/bin/env python3
"""
Setup script for OpenRouter integration with PersonaMem
"""

import os
import yaml
import json
import argparse
from pathlib import Path


def create_directory_structure():
    """Create necessary directories"""
    directories = [
        "api_tokens",
        "data/results",
        "docs",
        "logs",
        "configs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")


def create_enhanced_config():
    """Create enhanced configuration file"""
    config = {
        "datasets": {
            "data_dir": "data/synthetic",
            "topics": "therapy",
            "therapy_source_dir": "data/source/HOPE_WSDM_2022/conversations",
            "legal_source_dir": "data/source/legal",
            "writing_source_dir": "data/source/creative_writing/human_wp_stories_cleaned.json",
            "coding_source_dir": "data/source/leetcode_hard/java_solutions.txt",
            "email_source_dir": "data/source/email_records/filtered_files_sager-e.txt",
            "persona_file": "data/source/Persona_Hub_200000.jsonl",
            "random_questions_file": "data/random_questions.txt",
            "random_code_questions_file": "data/random_code_questions.txt",
            "random_contexts_file": "data/irrelevant_contexts.json"
        },
        "models": {
            "llm_model": "gpt-4o",
            "use_openrouter": True,
            "openrouter_config": {
                "fallback_models": ["gpt-4o-mini", "claude-3-haiku", "gemini-1.5-flash"],
                "cost_optimization": True,
                "max_retries": 3,
                "timeout": 60,
                "model_selection_strategy": "balanced"  # cheapest, fastest, best_quality, balanced
            }
        },
        "inference": {
            "verbose": False,
            "print_every": 50,
            "use_function_calling": True,
            "function_calling_models": ["gpt-4o", "claude-3-5-sonnet", "gemini-1.5-pro", "gemini-2.0-flash"],
            "enhanced_evaluation": True,
            "save_function_results": True
        },
        "openrouter": {
            "base_url": "https://openrouter.ai/api/v1",
            "enable_fallback": True,
            "cost_tracking": True,
            "rate_limit_handling": True,
            "retry_strategy": {
                "max_retries": 3,
                "backoff_factor": 2,
                "retry_on_errors": ["rate_limit", "timeout", "server_error"]
            }
        },
        "logging": {
            "level": "INFO",
            "file": "logs/personamem.log",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "max_file_size": "10MB",
            "backup_count": 5
        },
        "performance": {
            "batch_size": 10,
            "concurrent_requests": 5,
            "cache_responses": True,
            "cache_ttl": 3600
        }
    }
    
    with open("config_enhanced.yaml", "w") as f:
        yaml.dump(config, f, default_flow_style=False, indent=2)
    
    print("✓ Created enhanced configuration: config_enhanced.yaml")


def create_model_configs():
    """Create model-specific configurations"""
    model_configs = {
        "openai_models": {
            "gpt-4o": {
                "max_tokens": 4096,
                "temperature": 0.1,
                "supports_functions": True,
                "cost_per_1k_tokens": {"input": 0.005, "output": 0.015}
            },
            "gpt-4o-mini": {
                "max_tokens": 4096,
                "temperature": 0.1,
                "supports_functions": True,
                "cost_per_1k_tokens": {"input": 0.00015, "output": 0.0006}
            },
            "o1": {
                "max_tokens": 4096,
                "temperature": 1.0,
                "supports_functions": False,
                "cost_per_1k_tokens": {"input": 0.015, "output": 0.06}
            }
        },
        "anthropic_models": {
            "claude-3-5-sonnet": {
                "max_tokens": 4096,
                "temperature": 0.1,
                "supports_functions": True,
                "cost_per_1k_tokens": {"input": 0.003, "output": 0.015}
            },
            "claude-3-haiku": {
                "max_tokens": 4096,
                "temperature": 0.1,
                "supports_functions": False,
                "cost_per_1k_tokens": {"input": 0.00025, "output": 0.00125}
            }
        },
        "google_models": {
            "gemini-2.0-flash": {
                "max_tokens": 4096,
                "temperature": 0.1,
                "supports_functions": True,
                "cost_per_1k_tokens": {"input": 0.00075, "output": 0.003}
            },
            "gemini-1.5-pro": {
                "max_tokens": 4096,
                "temperature": 0.1,
                "supports_functions": True,
                "cost_per_1k_tokens": {"input": 0.00125, "output": 0.005}
            }
        },
        "meta_models": {
            "llama-3.1-405b": {
                "max_tokens": 4096,
                "temperature": 0.1,
                "supports_functions": False,
                "cost_per_1k_tokens": {"input": 0.003, "output": 0.003}
            }
        }
    }
    
    with open("configs/model_configs.json", "w") as f:
        json.dump(model_configs, f, indent=2)
    
    print("✓ Created model configurations: configs/model_configs.json")


def create_api_key_template():
    """Create API key template files"""
    api_key_template = """# API Keys for PersonaMem
# Replace with your actual API keys

# OpenRouter API Key (required for enhanced functionality)
# Get from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_key_here

# Individual provider keys (optional if using OpenRouter)
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here

# Instructions:
# 1. Copy this file to .env
# 2. Replace the placeholder values with your actual API keys
# 3. Make sure .env is in your .gitignore file
"""
    
    with open("api_tokens/api_keys_template.txt", "w") as f:
        f.write(api_key_template)
    
    # Create individual key files for backward compatibility
    key_files = [
        "openrouter_key.txt",
        "openai_key.txt", 
        "claude_key.txt",
        "gemini_key.txt"
    ]
    
    for key_file in key_files:
        key_path = f"api_tokens/{key_file}"
        if not os.path.exists(key_path):
            with open(key_path, "w") as f:
                f.write("your_api_key_here")
    
    print("✓ Created API key templates in api_tokens/")
    print("  Please update the API keys before running the system")


def create_run_scripts():
    """Create convenient run scripts"""
    
    # Enhanced evaluation script
    enhanced_eval_script = """#!/bin/bash
# Enhanced PersonaMem Evaluation with OpenRouter

MODEL=${1:-"gpt-4o"}
BENCHMARK_SIZE=${2:-"128k"}
USE_FUNCTIONS=${3:-"true"}

echo "Running enhanced PersonaMem evaluation..."
echo "Model: $MODEL"
echo "Benchmark size: $BENCHMARK_SIZE"
echo "Function calling: $USE_FUNCTIONS"

QUESTION_PATH="data/questions_${BENCHMARK_SIZE}.csv"
CONTEXT_PATH="data/shared_contexts_${BENCHMARK_SIZE}.jsonl"
RESULT_PATH="data/results/enhanced_eval_${BENCHMARK_SIZE}_${MODEL}_$(date +%Y%m%d_%H%M%S).csv"

python openrouter_enhanced_inference.py \\
    --model "$MODEL" \\
    --step "evaluate" \\
    --question_path "$QUESTION_PATH" \\
    --context_path "$CONTEXT_PATH" \\
    --result_path "$RESULT_PATH" \\
    $([ "$USE_FUNCTIONS" = "true" ] && echo "--use_functions") \\
    --verbose

echo "Results saved to: $RESULT_PATH"
"""
    
    with open("run_enhanced_eval.sh", "w") as f:
        f.write(enhanced_eval_script)
    os.chmod("run_enhanced_eval.sh", 0o755)
    
    # Model comparison script
    comparison_script = """#!/bin/bash
# Compare multiple models with enhanced evaluation

MODELS=("gpt-4o" "claude-3-5-sonnet" "gemini-2.0-flash" "gpt-4o-mini")
BENCHMARK_SIZE="128k"

echo "Starting model comparison..."

for MODEL in "${MODELS[@]}"; do
    echo "Evaluating model: $MODEL"
    ./run_enhanced_eval.sh "$MODEL" "$BENCHMARK_SIZE" "true"
    echo "Completed: $MODEL"
    echo "---"
done

echo "All model evaluations completed!"
echo "Results are in data/results/"
"""
    
    with open("compare_models.sh", "w") as f:
        f.write(comparison_script)
    os.chmod("compare_models.sh", 0o755)
    
    print("✓ Created run scripts:")
    print("  - run_enhanced_eval.sh")
    print("  - compare_models.sh")


def create_requirements_enhanced():
    """Create enhanced requirements file"""
    requirements = """# Enhanced PersonaMem Requirements
# Core dependencies
openai>=1.42.0
anthropic>=0.40.0
google-generativeai>=0.1.0rc1
tiktoken>=0.7.0
pyyaml>=6.0.2
tqdm>=4.66.5
numpy>=1.24.4
pandas>=2.0.0
torch>=2.4.0

# Enhanced functionality
requests>=2.32.3
aiohttp>=3.9.0
asyncio-throttle>=1.0.2
tenacity>=8.2.0
python-dotenv>=1.0.0

# Data processing
json-repair>=0.30.0
regex>=2024.9.11
beautifulsoup4>=4.13.3

# Monitoring and logging
structlog>=23.1.0
prometheus-client>=0.19.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Optional: Local model support
transformers>=4.46.3
sentence-transformers>=3.2.1
"""
    
    with open("requirements_enhanced.txt", "w") as f:
        f.write(requirements)
    
    print("✓ Created enhanced requirements: requirements_enhanced.txt")


def create_docker_setup():
    """Create Docker setup files"""
    dockerfile = """FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements_enhanced.txt .
RUN pip install --no-cache-dir -r requirements_enhanced.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p api_tokens data/results logs

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Expose port for monitoring (optional)
EXPOSE 8080

# Default command
CMD ["python", "openrouter_enhanced_inference.py", "--help"]
"""
    
    docker_compose = """version: '3.8'

services:
  personamem:
    build: .
    container_name: personamem-enhanced
    volumes:
      - ./data:/app/data
      - ./api_tokens:/app/api_tokens
      - ./logs:/app/logs
      - ./configs:/app/configs
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    command: python openrouter_enhanced_inference.py --model gpt-4o --step evaluate --verbose
    
  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    container_name: personamem-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data:
"""
    
    with open("Dockerfile", "w") as f:
        f.write(dockerfile)
    
    with open("docker-compose.yml", "w") as f:
        f.write(docker_compose)
    
    print("✓ Created Docker setup files:")
    print("  - Dockerfile")
    print("  - docker-compose.yml")


def main():
    parser = argparse.ArgumentParser(description="Setup PersonaMem with OpenRouter integration")
    parser.add_argument("--full", action="store_true", help="Create all setup files")
    parser.add_argument("--config", action="store_true", help="Create configuration files")
    parser.add_argument("--scripts", action="store_true", help="Create run scripts")
    parser.add_argument("--docker", action="store_true", help="Create Docker setup")
    parser.add_argument("--keys", action="store_true", help="Create API key templates")
    
    args = parser.parse_args()
    
    if not any(vars(args).values()):
        args.full = True
    
    print("🚀 Setting up PersonaMem with OpenRouter integration...")
    print()
    
    # Create directory structure
    create_directory_structure()
    print()
    
    if args.full or args.config:
        create_enhanced_config()
        create_model_configs()
        create_requirements_enhanced()
        print()
    
    if args.full or args.keys:
        create_api_key_template()
        print()
    
    if args.full or args.scripts:
        create_run_scripts()
        print()
    
    if args.full or args.docker:
        create_docker_setup()
        print()
    
    print("✅ Setup completed successfully!")
    print()
    print("Next steps:")
    print("1. Update API keys in api_tokens/ directory")
    print("2. Install dependencies: pip install -r requirements_enhanced.txt")
    print("3. Run evaluation: ./run_enhanced_eval.sh gpt-4o 128k true")
    print("4. Check results in data/results/")
    print()
    print("For Docker deployment:")
    print("1. docker-compose up --build")
    print()
    print("Documentation available in docs/ directory")


if __name__ == "__main__":
    main()
