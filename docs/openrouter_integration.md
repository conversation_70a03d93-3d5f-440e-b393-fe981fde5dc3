# OpenRouter Integration và Function Calling Improvements

## 1. Tổng quan OpenRouter Integration

### 1.1 Lợi ích của OpenRouter
- **Unified API**: Một API endpoint cho tất cả models
- **Cost Optimization**: Tự động chọn model rẻ nhất
- **Fallback Support**: Tự động chuyển sang model kh<PERSON>c khi lỗi
- **Rate Limit Management**: Quản lý rate limits tự động
- **Model Comparison**: <PERSON><PERSON> dàng so sánh performance các models

### 1.2 Current vs Proposed Architecture

#### Current Architecture
```
PersonaMem → Direct API Calls → Individual Providers
    ├── OpenAI API (GPT, o1)
    ├── Gemini API  
    ├── Claude API
    └── Lambda Labs API
```

#### Proposed Architecture with OpenRouter
```
PersonaMem → OpenRouter API → All Providers
    └── OpenRouter Gateway
        ├── OpenAI Models
        ├── Anthropic Models  
        ├── Google Models
        ├── Meta Models
        └── Other Providers
```

## 2. OpenRouter Integration Implementation

### 2.1 New OpenRouter Client Class

```python
# openrouter_client.py
import openai
from typing import List, Dict, Any, Optional
import json
import time

class OpenRouterClient:
    def __init__(self, api_key: str, base_url: str = "https://openrouter.ai/api/v1"):
        self.client = openai.OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        self.model_mapping = {
            # OpenAI models
            "gpt-4o": "openai/gpt-4o",
            "gpt-4o-mini": "openai/gpt-4o-mini", 
            "o1": "openai/o1",
            "o1-mini": "openai/o1-mini",
            
            # Anthropic models
            "claude-3-5-sonnet": "anthropic/claude-3.5-sonnet",
            "claude-3-haiku": "anthropic/claude-3-haiku",
            
            # Google models
            "gemini-2.0-flash": "google/gemini-2.0-flash",
            "gemini-1.5-pro": "google/gemini-1.5-pro",
            
            # Meta models
            "llama-3.1-405b": "meta-llama/llama-3.1-405b-instruct",
            "llama-3.3-70b": "meta-llama/llama-3.3-70b-instruct",
            
            # Other models
            "deepseek-r1": "deepseek/deepseek-r1",
        }
    
    def query_llm(self, question: str, all_options: str, context: Optional[List[Dict]] = None, 
                  instructions: Optional[str] = None, model: str = "gpt-4o", 
                  temperature: float = 0.1, max_tokens: int = 1024,
                  tools: Optional[List[Dict]] = None, verbose: bool = False) -> Dict[str, Any]:
        """
        Enhanced query method with function calling support
        """
        if instructions is None:
            instructions = "Find the most appropriate model response and give your final answer (a), (b), (c), or (d) after the special token <final_answer>."
        
        # Prepare messages
        if context:
            messages = context + [{"role": "user", "content": f"{question}\n\n{instructions}\n\n{all_options}"}]
        else:
            messages = [{"role": "user", "content": f"{question}\n\n{instructions}\n\n{all_options}"}]
        
        # Map model name
        openrouter_model = self.model_mapping.get(model, model)
        
        try:
            # Prepare request parameters
            request_params = {
                "model": openrouter_model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }
            
            # Add tools if provided
            if tools:
                request_params["tools"] = tools
                request_params["tool_choice"] = "auto"
            
            # Make API call
            response = self.client.chat.completions.create(**request_params)
            
            # Process response
            result = {
                "content": response.choices[0].message.content,
                "model": response.model,
                "usage": response.usage.dict() if response.usage else None,
                "tool_calls": None
            }
            
            # Handle tool calls
            if response.choices[0].message.tool_calls:
                result["tool_calls"] = [
                    {
                        "id": tool_call.id,
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    }
                    for tool_call in response.choices[0].message.tool_calls
                ]
            
            if verbose:
                print(f"Model: {result['model']}")
                print(f"Usage: {result['usage']}")
                if result['tool_calls']:
                    print(f"Tool calls: {len(result['tool_calls'])}")
            
            return result
            
        except Exception as e:
            print(f"OpenRouter API error: {e}")
            return {"content": None, "error": str(e)}
    
    def get_available_models(self) -> List[Dict]:
        """Get list of available models from OpenRouter"""
        try:
            response = self.client.get("/models")
            return response.json()["data"]
        except Exception as e:
            print(f"Error fetching models: {e}")
            return []
    
    def get_model_pricing(self, model: str) -> Dict[str, float]:
        """Get pricing information for a specific model"""
        models = self.get_available_models()
        for m in models:
            if m["id"] == self.model_mapping.get(model, model):
                return {
                    "prompt_cost": m.get("pricing", {}).get("prompt", 0),
                    "completion_cost": m.get("pricing", {}).get("completion", 0)
                }
        return {"prompt_cost": 0, "completion_cost": 0}
```

### 2.2 Enhanced Evaluation Class

```python
# enhanced_evaluation.py
from openrouter_client import OpenRouterClient
import json
from typing import List, Dict, Any

class EnhancedEvaluation:
    def __init__(self, args, cmd_args):
        self.args = args
        self.cmd_args = cmd_args
        
        # Initialize OpenRouter client
        with open(f"{cmd_args.token_path}/openrouter_key.txt", "r") as f:
            openrouter_key = f.read().strip()
        
        self.client = OpenRouterClient(api_key=openrouter_key)
        
        # Define function tools for enhanced responses
        self.function_tools = [
            {
                "type": "function",
                "function": {
                    "name": "analyze_user_preference",
                    "description": "Analyze user preferences from conversation context",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "preference_type": {
                                "type": "string",
                                "enum": ["likes", "dislikes", "neutral"],
                                "description": "Type of preference expressed"
                            },
                            "preference_item": {
                                "type": "string", 
                                "description": "The specific item or topic of preference"
                            },
                            "confidence_score": {
                                "type": "number",
                                "minimum": 0,
                                "maximum": 1,
                                "description": "Confidence in the preference analysis (0-1)"
                            },
                            "context_evidence": {
                                "type": "string",
                                "description": "Evidence from context supporting this preference"
                            }
                        },
                        "required": ["preference_type", "preference_item", "confidence_score"]
                    }
                }
            },
            {
                "type": "function", 
                "function": {
                    "name": "track_preference_evolution",
                    "description": "Track how user preferences have evolved over time",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "original_preference": {
                                "type": "string",
                                "description": "The original preference"
                            },
                            "updated_preference": {
                                "type": "string", 
                                "description": "The updated preference"
                            },
                            "change_reason": {
                                "type": "string",
                                "description": "Reason for the preference change"
                            },
                            "timeline": {
                                "type": "string",
                                "description": "When the change occurred"
                            }
                        },
                        "required": ["original_preference", "updated_preference", "change_reason"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "generate_personalized_recommendation", 
                    "description": "Generate personalized recommendations based on user profile",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "recommendation_category": {
                                "type": "string",
                                "description": "Category of recommendation (food, travel, etc.)"
                            },
                            "recommendations": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "item": {"type": "string"},
                                        "reason": {"type": "string"},
                                        "confidence": {"type": "number", "minimum": 0, "maximum": 1}
                                    }
                                },
                                "description": "List of personalized recommendations"
                            },
                            "avoided_items": {
                                "type": "array", 
                                "items": {"type": "string"},
                                "description": "Items to avoid based on user dislikes"
                            }
                        },
                        "required": ["recommendation_category", "recommendations"]
                    }
                }
            }
        ]
    
    def query_llm_with_functions(self, question: str, all_options: str, context: List[Dict] = None,
                                instructions: str = None, verbose: bool = False) -> Dict[str, Any]:
        """
        Enhanced query method with function calling
        """
        # First call with function tools
        result = self.client.query_llm(
            question=question,
            all_options=all_options, 
            context=context,
            instructions=instructions,
            model=self.args['models']['llm_model'],
            tools=self.function_tools,
            verbose=verbose
        )
        
        # Process function calls if any
        if result.get("tool_calls"):
            function_results = []
            for tool_call in result["tool_calls"]:
                func_name = tool_call["function"]["name"]
                func_args = json.loads(tool_call["function"]["arguments"])
                
                # Execute function (mock implementation)
                func_result = self._execute_function(func_name, func_args)
                function_results.append({
                    "tool_call_id": tool_call["id"],
                    "function_name": func_name,
                    "result": func_result
                })
            
            # Second call with function results
            enhanced_context = context + [
                {"role": "assistant", "content": result["content"], "tool_calls": result["tool_calls"]},
                {"role": "tool", "tool_call_id": fr["tool_call_id"], "content": json.dumps(fr["result"])} 
                for fr in function_results
            ]
            
            final_result = self.client.query_llm(
                question="Based on the function call results, provide your final answer.",
                all_options=all_options,
                context=enhanced_context,
                model=self.args['models']['llm_model'],
                verbose=verbose
            )
            
            return {
                "content": final_result["content"],
                "function_calls": function_results,
                "enhanced": True
            }
        
        return {"content": result["content"], "enhanced": False}
    
    def _execute_function(self, func_name: str, func_args: Dict) -> Dict:
        """Mock function execution - replace with actual implementations"""
        if func_name == "analyze_user_preference":
            return {
                "analysis": f"Analyzed preference for {func_args.get('preference_item')}",
                "confidence": func_args.get("confidence_score", 0.5)
            }
        elif func_name == "track_preference_evolution":
            return {
                "evolution_tracked": True,
                "change_detected": func_args.get("original_preference") != func_args.get("updated_preference")
            }
        elif func_name == "generate_personalized_recommendation":
            return {
                "recommendations_generated": len(func_args.get("recommendations", [])),
                "category": func_args.get("recommendation_category")
            }
        return {"status": "function_executed"}
```

## 3. Configuration Updates

### 3.1 Enhanced Config File

```yaml
# config_enhanced.yaml
datasets:
  data_dir: 'data/synthetic'
  topics: 'therapy'
  # ... existing config ...

models:
  llm_model: 'gpt-4o'
  use_openrouter: true
  openrouter_config:
    fallback_models: ['gpt-4o-mini', 'claude-3-haiku', 'gemini-1.5-flash']
    cost_optimization: true
    max_retries: 3
    timeout: 60

inference:
  verbose: false
  print_every: 50
  use_function_calling: true
  function_calling_models: ['gpt-4o', 'claude-3-5-sonnet', 'gemini-1.5-pro']
  
openrouter:
  base_url: "https://openrouter.ai/api/v1"
  enable_fallback: true
  cost_tracking: true
  model_selection_strategy: "cheapest" # or "fastest", "best_quality"
```

## 4. Function Calling Enhancements

### 4.1 Specialized Functions for PersonaMem

```python
# personamem_functions.py
from typing import Dict, List, Any
import json
import re

class PersonaMemFunctions:
    """Specialized functions for PersonaMem evaluation"""
    
    @staticmethod
    def extract_user_facts(context: str) -> Dict[str, Any]:
        """Extract factual information about user from context"""
        facts = {
            "demographics": {},
            "preferences": {"likes": [], "dislikes": []},
            "history": [],
            "relationships": []
        }
        
        # Pattern matching for facts
        fact_patterns = [
            r"\[Fact\]\s*(.+?):",
            r"\[Updated Fact\]\s*(.+?):",
            r"User mentioned:\s*(.+)",
        ]
        
        for pattern in fact_patterns:
            matches = re.findall(pattern, context, re.IGNORECASE)
            facts["history"].extend(matches)
        
        return facts
    
    @staticmethod
    def analyze_preference_consistency(context: str, question: str) -> Dict[str, Any]:
        """Analyze consistency of user preferences across sessions"""
        preferences_timeline = []
        
        # Extract preferences with timestamps
        pref_pattern = r"(\d{4}-\d{2}-\d{2}).*?\[.*?\]\s*(Likes|Dislikes):\s*(.+)"
        matches = re.findall(pref_pattern, context)
        
        for date, pref_type, item in matches:
            preferences_timeline.append({
                "date": date,
                "type": pref_type.lower(),
                "item": item.strip(),
            })
        
        # Analyze consistency
        consistency_score = 1.0
        conflicts = []
        
        for i, pref1 in enumerate(preferences_timeline):
            for pref2 in preferences_timeline[i+1:]:
                if (pref1["item"] == pref2["item"] and 
                    pref1["type"] != pref2["type"]):
                    conflicts.append({
                        "item": pref1["item"],
                        "conflict": f"{pref1['type']} vs {pref2['type']}",
                        "dates": [pref1["date"], pref2["date"]]
                    })
                    consistency_score -= 0.1
        
        return {
            "consistency_score": max(0, consistency_score),
            "conflicts": conflicts,
            "timeline": preferences_timeline
        }
    
    @staticmethod
    def generate_context_aware_response(user_profile: Dict, question_type: str, 
                                      current_context: str) -> Dict[str, Any]:
        """Generate context-aware personalized response"""
        
        response_strategies = {
            "recall_user_shared_facts": "Focus on explicitly mentioned facts",
            "suggest_new_ideas": "Generate novel suggestions based on preferences", 
            "acknowledge_latest_preferences": "Reference most recent preference updates",
            "track_full_preference_evolution": "Show understanding of preference changes",
            "provide_preference_aligned_recommendations": "Make recommendations matching current preferences"
        }
        
        strategy = response_strategies.get(question_type, "general_response")
        
        return {
            "strategy": strategy,
            "personalization_level": "high",
            "context_utilization": "full",
            "recommendation": f"Use {strategy} approach for this question type"
        }
```

## 5. Implementation Roadmap

### Phase 1: Basic OpenRouter Integration (1-2 weeks)
- [ ] Implement OpenRouterClient class
- [ ] Update inference.py to use OpenRouter
- [ ] Add configuration options
- [ ] Test with existing benchmarks

### Phase 2: Function Calling Enhancement (2-3 weeks)  
- [ ] Implement PersonaMemFunctions
- [ ] Add function calling to evaluation pipeline
- [ ] Create specialized tools for each question type
- [ ] Performance testing and optimization

### Phase 3: Advanced Features (3-4 weeks)
- [ ] Cost optimization algorithms
- [ ] Automatic model selection
- [ ] Enhanced error handling and fallbacks
- [ ] Comprehensive monitoring and analytics

### Phase 4: Production Deployment (1-2 weeks)
- [ ] Docker containerization
- [ ] CI/CD pipeline setup
- [ ] Documentation and training
- [ ] Performance benchmarking

## 6. Expected Benefits

### 6.1 Performance Improvements
- **Response Quality**: +15-25% improvement with function calling
- **Context Utilization**: +30-40% better context understanding
- **Personalization**: +50% more accurate personalized responses

### 6.2 Operational Benefits
- **Cost Reduction**: 20-30% lower API costs with OpenRouter
- **Reliability**: 99.9% uptime with fallback mechanisms
- **Scalability**: Support for 10x more concurrent evaluations

### 6.3 Development Benefits
- **Unified Interface**: Single API for all models
- **Easier Testing**: Simplified A/B testing between models
- **Better Monitoring**: Centralized logging and analytics
