# PersonaMem - T<PERSON><PERSON> liệu Kiến trúc Hệ thống

## 1. Tổng quan Hệ thống

PersonaMem là một benchmark đánh giá khả năng của các Large Language Models (LLMs) trong việc:
- <PERSON><PERSON> luận profile người dùng động (Dynamic User Profiling)
- <PERSON>ạo phản hồi cá nhân hóa (Personalized Responses)
- Theo dõi sự thay đổi preferences qua nhiều session

### 1.1 Mục tiêu chính
- Đ<PERSON>h giá 15+ LLMs state-of-the-art (GPT-4.5, o1, Llama-4, DeepSeek-R1, Gemini-2, Claude-3.7, etc.)
- Benchmark trên 7 loại query khác nhau
- Hỗ trợ context length từ 32k đến 1M tokens

## 2. Kiến trúc Hệ thống

### 2.1 Kiến trúc Tổng thể

```
┌─────────────────────────────────────────────────────────────┐
│                    PersonaMem System                        │
├─────────────────────────────────────────────────────────────┤
│  Data Generation Pipeline                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Persona Hub │  │ Source Data │  │ Conversation│        │
│  │ (200k)      │  │ Processing  │  │ Generation  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  Multi-LLM Inference Engine                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ OpenAI API  │  │ Gemini API  │  │ Claude API  │        │
│  │ (GPT, o1)   │  │ (Gemini-2)  │  │ (Claude-3.7)│        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│  ┌─────────────┐  ┌─────────────┐                         │
│  │ Lambda Labs │  │ DeepSeek    │                         │
│  │ (Llama)     │  │ (R1)        │                         │
│  └─────────────┘  └─────────────┘                         │
├─────────────────────────────────────────────────────────────┤
│  Evaluation & Benchmarking                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Question    │  │ Context     │  │ Result      │        │
│  │ Generation  │  │ Management  │  │ Analysis    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Core Components

#### 2.2.1 Data Generation Pipeline (`prepare_data.py`)
- **Persona Expansion**: Mở rộng persona từ Persona Hub
- **Multi-session Conversations**: Tạo cuộc hội thoại qua nhiều thời điểm
- **Personal History Tracking**: Theo dõi lịch sử cá nhân qua thời gian
- **Topic Diversification**: Hỗ trợ nhiều chủ đề (therapy, legal, writing, coding, email)

#### 2.2.2 LLM Query Engine (`query_llm.py`)
- **Multi-threaded Processing**: Quản lý nhiều thread cho các loại task khác nhau
- **Assistant Management**: Tạo và quản lý OpenAI Assistants
- **Timeout Handling**: Xử lý timeout cho API calls
- **Response Processing**: Xử lý và format response từ LLMs

#### 2.2.3 Inference Engine (`inference.py`)
- **Multi-provider Support**: Hỗ trợ OpenAI, Gemini, Claude, Lambda Labs
- **Context Management**: Quản lý context với độ dài khác nhau (32k-1M tokens)
- **Evaluation Pipeline**: Pipeline đánh giá tự động
- **Result Processing**: Xử lý và lưu trữ kết quả

### 2.3 Data Flow

```
Persona Hub → Persona Expansion → Personal History Generation
     ↓
Multi-session Conversation Generation → QA Pair Generation
     ↓
Context Preparation → LLM Inference → Result Evaluation
     ↓
Performance Analysis → Benchmark Results
```

## 3. Thuật toán và Pretrain

### 3.1 Không có Pretrain
PersonaMem **KHÔNG phải là một pretrain system**. Đây là một:
- **Benchmark evaluation framework**
- **Synthetic data generation pipeline**
- **Multi-LLM testing platform**

### 3.2 Thuật toán chính

#### 3.2.1 Persona-oriented Conversation Generation
```python
# Thuật toán tạo conversation
1. Load persona từ Persona Hub
2. Expand persona với context và timeline
3. Generate personal history (init → week → month → year)
4. Create multi-session conversations
5. Inject preference changes over time
6. Generate QA pairs for evaluation
```

#### 3.2.2 Topological Sorting Algorithm
- Sắp xếp conversation blocks theo timestamp
- Tạo nhiều variants của conversation sequence
- Đảm bảo tính nhất quán thời gian

#### 3.2.3 Context Distance Calculation
- Tính khoảng cách từ question đến relevant information
- Đo lường theo blocks và tokens
- Phân tích tỷ lệ irrelevant context

## 4. Yêu cầu Tài nguyên

### 4.1 Computational Resources

#### 4.1.1 CPU Requirements
- **Minimum**: 8 cores, 16GB RAM
- **Recommended**: 16+ cores, 32GB+ RAM
- **Optimal**: 32+ cores, 64GB+ RAM

#### 4.1.2 GPU Requirements (Optional)
- Không bắt buộc GPU cho inference (sử dụng API)
- GPU có thể hữu ích cho:
  - Local model inference
  - Embedding computation
  - Data preprocessing

#### 4.1.3 Storage Requirements
- **Minimum**: 50GB SSD
- **Recommended**: 200GB+ SSD
- **Data breakdown**:
  - Persona Hub: ~5GB
  - Source data: ~10GB
  - Generated conversations: ~20-100GB
  - Results and logs: ~10-50GB

### 4.2 API Resources

#### 4.2.1 API Costs (Estimated per 1000 evaluations)
- **GPT-4o**: $50-100
- **GPT-4.5**: $100-200
- **Claude-3.7**: $30-80
- **Gemini-2.0**: $20-60
- **DeepSeek-R1**: $10-30

#### 4.2.2 Rate Limits
- OpenAI: 10,000 RPM (requests per minute)
- Gemini: 1,000 RPM
- Claude: 5,000 RPM
- Lambda Labs: 100 RPM

### 4.3 Network Requirements
- **Bandwidth**: 100Mbps+ recommended
- **Latency**: <100ms to API endpoints
- **Reliability**: 99.9% uptime required

## 5. Performance Characteristics

### 5.1 Benchmark Sizes
- **32k tokens**: ~10k questions, 2-4 hours processing
- **128k tokens**: ~50k questions, 8-16 hours processing  
- **1M tokens**: ~200k questions, 24-48 hours processing

### 5.2 Scalability
- **Horizontal**: Có thể chạy parallel trên nhiều machines
- **Vertical**: Memory-bound, cần RAM cao cho large contexts
- **API-bound**: Bị giới hạn bởi rate limits của providers

## 6. Monitoring và Logging

### 6.1 Logging System
- **Utils.py**: Color-coded console logging
- **Error tracking**: Exception handling và retry logic
- **Progress tracking**: tqdm progress bars
- **Verbose modes**: Chi tiết debug information

### 6.2 Metrics Tracking
- **Token usage**: Theo dõi token consumption
- **API latency**: Đo thời gian response
- **Success rates**: Tỷ lệ thành công của API calls
- **Quality metrics**: Đánh giá chất lượng generated data

## 7. Security và Compliance

### 7.1 API Key Management
- Keys stored in separate files
- Environment-based configuration
- No hardcoded credentials

### 7.2 Data Privacy
- Synthetic data generation
- No real user data
- GDPR-compliant approach

## 8. Deployment Options

### 8.1 Local Development
```bash
# Setup environment
pip install -r requirements.txt
# Configure API keys
mkdir api_tokens
echo "your_key" > api_tokens/openai_key.txt
# Run benchmark
python inference.py --step prepare --model gpt-4o
```

### 8.2 Cloud Deployment
- **AWS**: EC2 instances với high memory
- **Google Cloud**: Compute Engine với GPU optional
- **Azure**: Virtual Machines với managed APIs

### 8.3 Container Deployment
```dockerfile
FROM python:3.9
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . /app
WORKDIR /app
CMD ["python", "inference.py"]
```
