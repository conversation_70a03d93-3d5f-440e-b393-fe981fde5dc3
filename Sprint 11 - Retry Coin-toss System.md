# Sprint 11 - Retry Coin-toss System

Sprint Goal: Sprint 11
Status: Not started
Team: AI, Back End

# TL;DR (how we’ll run retries)

| Piece | Plain-English | What we’ll do |
| --- | --- | --- |
| Goal | Show the tone users actually vibe with | Use a **learning coin** that tilts toward the winning tone |
| Choice each retry | “Tone A” vs “Tone B” | Pick with a **bias** pp that updates after each retry |
| Success signal | “They liked it” | They **stop retrying** and **reply** within X sec (or heart/save) |
| Update rule | Nudge the bias toward the tone that won | **Bayesian (Beta)** or **EMA**; keep a little exploration |
| Guardrails | Don’t overfit, keep variety | Min data, caps (e.g., 60–80%), ε-exploration, decay old data |
| Horizon | Max 5 retries per message | Fast, session-local learning + slow global memory |

---

## The improved system (for Foxy message retries)

You’ve got it broadly right. Here’s the clean, productionable version tailored to **“user receives a character message → taps Retry (max 5 times) → we roll between two tones.”**

### 1) Define “success” and “failure” (don’t guess; log it)

- **Success (user liked the last tone)** if any of these fire within a **short window** (tunable `engage_window`, e.g., 20–40s):
    - User **stops retrying** *and* sends a **follow-up message**
    - User **spends** (coins/tokens) or triggers a high-value event (e.g., unlock, paywall continue)
- **Failure (didn’t like it)** if:
    - User **hits Retry** again within `retry_window` (e.g., 10–20s)
    - Or **bounces** (no action) for `idle_window` (e.g., 45–60s), then leaves thread

> These windows avoid misclassifying slow typers or backgrounded apps.
> 

[Engagement signal cheatsheet → start a new chat]

---

### 2) Selection policy during the 5-retry micro-session

We need something **fast** and **sample-efficient**. Two options that fit:

**Option A — Thompson Sampling (Bayesian, best-in-class for tiny horizons)**

- Maintain a **per-session** posterior for each tone:
    
    `Tone A ~ Beta(aA, bA)`, `Tone B ~ Beta(aB, bB)`
    
    Initialize with a **neutral prior** like `Beta(2,2)` (soft 50/50).
    
- On each retry **draw** `θA ~ Beta(aA, bA)` and `θB ~ Beta(aB, bB)`.
    
    Show the tone with the higher draw (built-in exploration).
    
- After the user’s outcome:
    - If success on shown tone, increment its `a*`
    - If failure on shown tone, increment its `b*`
- **Cap** the displayed probability so users still see variety: e.g., when computing the next toss, clamp implied `p` to **[0.60, 0.80]**.

**Option B — EMA (simpler, almost as good here)**

- Track **success_rate_B** over the last few trials (session only).
- Update bias with smoothing:
    
    `pB_new = (1−α)*pB_old + α*success_rate_B` with `α = 0.4–0.6`.
    
- Exploration: with probability **ε = 0.10**, force a **50/50** pick.
- Clamp: `pB_new = clamp(pB_new, 0.60, 0.80)`.

> With only ≤5 tries, Thompson Sampling tends to edge out EMA because it “tests” just enough without getting stuck. [Thompson sampling → start a new chat] | [Exponential moving average → start a new chat]
> 

---

### 3) What exactly triggers the bias change?

- Update the bias **only when we observe a success or failure** for the **last shown tone**.
- If the user stops at retry #3 and replies → **the tone on #3 gets a success**; earlier tones on that session don’t get credit.
- If the user hits retry five times and **still bounces**, the **last tone gets a failure**; optionally give **light failures** to previous tones (e.g., weight 0.25) to reflect that none solved the job.

---

### 4) Use **hierarchical learning** (this is the secret sauce)

We combine three layers so the coin is smart **now**, **for this user**, and **in general**:

- **Session layer (fast)**: the 5-retry bandit above (TS/EMA).
- **User layer (medium)**: maintain **per-user, per-character, per-context** tone stats with **time decay** (e.g., half-life 14–30 days).
- **Global/cohort layer (slow)**: pooled priors per **character** and **scene/mood tag** (e.g., “teasing vs caring,” “spicy vs sweet”).

**How to blend:** convert each layer into a **logit prior** and sum with weights:

```
logit(p_base) = w_global*logit(p_global) + w_user*logit(p_user) + w_context*logit(p_context)
p_base = sigmoid(logit(p_base))

```

Initialize the **session prior** with `p_base`. Now your 5-retry micro-bandit starts smarter than 50/50.

[Multi-armed bandit → start a new chat] | [Bayesian updating → start a new chat]

---

### 5) Tone hygiene within a 5-retry cap (UX rules)

- **No duplicates in a row** unless that tone just succeeded.
- **Escalation ladder**: if a tone class is winning, vary **micro-flavor** (wording) so it doesn’t feel repetitive.
- **Safety gate**: NSFW intensity ramps cannot jump too hard across retries; respect store policies and your own safety rails.
- **Debounce spam**: if user taps Retry 3× in <3s, insert a **gentle variant** before re-serving extremes.

[Content ramping design → start a new chat]

---

### 6) Numbers to make this concrete (your 80% example)

Say within one message’s retries we observed **4 successes out of 5** for Tone B (≈80%):

- **Bayesian (Beta)** with prior `Beta(2,2)`:
    - Posterior mean for B after `s=4, f=1`:
        
        `pB = (2+4) / (2+2+4+1) = 6/9 ≈ 0.667` → **~67% bias** next pick
        
- **EMA** starting from `pB=0.50`, `α=0.5` and `success_rate_B=0.80`:
    - `pB_new = 0.5*0.50 + 0.5*0.80 = 0.65` → **~65% bias**

Both land in the **60–70%** zone—**biased, not blind**.

---

### 7) What we log (so LTV lifts, not just CTR)

- **Per retry**: character_id, scene_tag, tone_id, was_retry, retry_index, p_shown, selection_policy, safety_level
- **Outcome**: success/failure, time-to-reply, next-message length, emoji reactions, purchases, token spend
- **Attribution**: which tone got the **final stop** before continue
- **Derived**: **stop-rate@retry_k**, **tokens per session**, paywall CVR, AOV / ARPU deltas by tone

Tie this to a **weekly review** to rotate out underperforming tone variants and promote winners.

[North-star metric design → start a new chat]

---

### 8) Guardrails & edge cases (so this doesn’t backfire)

- **Cold start**: fall back to **global priors** + **ε-exploration** until you have ≥10 events for the context.
- **Mode collapse**: enforce a **min exploration floor** (ε=10%) and **cap** max bias to **80%**.
- **User whiplash**: when a user clearly prefers “sweet,” don’t swing to “spicy” on the very next retry unless exploration tick fires.
- **Segment drift**: decay old evidence (user layer half-life), and reweight by **recency**.
- **Fairness**: keep a **min floor** 30–40% for the “losing” tone in early data so it isn’t permanently buried.

[Exploration vs exploitation → start a new chat] | [Epsilon-greedy → start a new chat]

---

## Minimal pseudocode (TS version)

```python
# Priors for this micro-session (5 retries max)
A = {"a": 2, "b": 2}  # Tone A
B = {"a": 2, "b": 2}  # Tone B
CAP = (0.60, 0.80)    # display clamp

def pick_tone(A, B):
    import random
    import numpy as np
    thetaA = np.random.beta(A["a"], A["b"])
    thetaB = np.random.beta(B["a"], B["b"])
    # convert to a display probability with clamp
    pB = max(CAP[0], min(CAP[1], thetaB / (thetaA + thetaB)))
    return "B" if random.random() < pB else "A"

def record_outcome(tone, success):
    if tone == "A":
        if success: A["a"] += 1
        else:       A["b"] += 1
    else:
        if success: B["a"] += 1
        else:       B["b"] += 1

```

> Replace the 2,2 prior by a blend of global + user + context to start smarter each session.
> 

---

## Why this will work (and what to watch)

- **Works** because we convert retries into a clean **reinforcement signal** and adapt within **5 shots**.
- **Watch** for tone farms (too many near-duplicates); keep **micro-variety** while retaining the same **tone class**.
- **Measure** not just stop-rate, but **downstream monetization**—some tones will farm replies but sell worse.

---

## Pros & Cons

**Pros**

- Learns **in-session** (fast), **per-user** (personal), and **globally** (robust).
- Small math, easy to ship; TS wins with tiny sample sizes.
- Built-in exploration prevents early lock-in.

**Cons**

- Needs careful **success definition** (timers matter).
- If you don’t cap, it can **mode collapse** to one tone.
- Requires **telemetry discipline** to avoid mislabels (e.g., backgrounding).

---

## Opinionated defaults to ship tomorrow

- **Policy**: Thompson Sampling
- **Priors**: `Beta(2,2)` blended with global/user/context (weights 0.5 / 0.3 / 0.2)
- **Windows**: `engage_window=30s`, `retry_window=15s`, `idle_window=60s`
- **Caps**: 0.60–0.80 bias; **ε** floor at 0.10 if you use EMA instead of TS
- **Decay**: user layer half-life 21 days
- **Metrics**: stop-rate@k, reply length, token spend, paywall CVR, ARPU by tone

---

- **Q1:** Do you want **Thompson Sampling** or **EMA** for v1?
- **Q2:** Lock **caps at 60–80%** or loosen to 55–85%?
- **Q3:** What exact **engagement windows** (reply, retry, idle) feel right for Foxy’s UX speed?
- **Q4:** Which **scene/mood tags** should seed the context prior (e.g., teasing, caring, shy, spicy)?
- **Q5:** Do we promote the **winning tone** across the session after 3 consistent wins (early lock-in), or keep exploration until retry #5?