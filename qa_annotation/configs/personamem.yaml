{
    "port": 9001,

    "server_name": "potato annotator",

    "annotation_task_name": "PersonaMem Validation",

    # <PERSON>tat<PERSON> will write the annotation file for all annotations to this
    # directory, as well as per-annotator output files and state information
    # necessary to restart annotation.
    "output_annotation_dir": "annotation_output/full/",

    # The output format for the all-annotator data. Allowed formats are:
    # * jsonl
    # * json (same output as jsonl)
    # * csv
    # * tsv
    #
    "output_annotation_format": "tsv",

    # If annotators are using a codebook, this will be linked at the top to the
    # instance for easy access
    "annotation_codebook_url": "",

    "data_files": [
       "data_files/personamem_annot_sample.csv"
    ],

    "item_properties": {
        "id_key": "id",
        "text_key": "text",
    },

  #list_as_text is used when the input text is actually a list of texts, usually used for best-worst-scaling
    "list_as_text": {
      "text_list_prefix_type": 'None',
      "horizontal": True,
    },

    "user_config": {

      "allow_all_users": True,

      "users": [  ],
    },

    #defining the ways annotators entering the annotation system
    "login": {
       "type": 'password',    #can be 'password' or 'url_direct'
       "url_argument": 'PROLIFIC_PID' # when the login type is set to 'url_direct', 'url_argument' must be setup for a direct url argument login
    },

    #the jumping-to-id function will be disabled if "jumping_to_id_disabled" is True
    "jumping_to_id_disabled": True,

  #the navigation bar will be hidden to the annotators if "hide_navbar" is True
    "hide_navbar": False,

  # define the surveyflow of the system, set up the pages before and after the data annotation page
    "surveyflow": {
      "on": True,
      "order" : ['pre_annotation', 'post_annotation'],
      "pre_annotation": ['surveyflow/intro.jsonl', 'surveyflow/instruction.jsonl'],
      "post_annotation": ['surveyflow/end.jsonl'],
      # If set, we will automatically generate testing questions similar to the annotation instances, but explicitly ask the annotator to choose one option
      "testing": [],
    },

    "automatic_assignment": {
      #whether do automatic task assignment for annotators, default False.
      "on": True,
      "output_filename": 'task_assignment.json',
      "sampling_strategy": 'random',
      "labels_per_instance": 3,
      "instance_per_annotator": 45,
      "test_question_per_annotator": 0, # you must set up the test question in surveyflow to use this function

      "users": [  ],
    },


    # How many seconds do you want the annotators spend on each instance, after
    # that, an alert will be sent per alert_time_each_instance seconds.
    "alert_time_each_instance": 10000000,
    "horizontal_key_bindings": true,

  "annotation_schemes": [
    {
        "annotation_type": "radio",
        "name": "s1_quality",
        "description": "The question is well-formed and corresponds to the type.",
        "labels": [
          { "name": "True 1️⃣", "key_value": "1" },
          { "name": "False 2️⃣", "key_value": "2" }
        ],
        "label_requirement": {
              "required": True
          }
    },
    {
        "annotation_type": "radio",
        "name": "s2_relevance",
        "description": "The question is relevant to the conversation and persona.",
        "labels": [
          { "name": "True 3️⃣", "key_value": "3" },
          { "name": "False 4️⃣", "key_value": "4" }
        ],
        "label_requirement": {
              "required": True
          }
    },
    {
        "annotation_type": "radio",
        "name": "s3_cresponse",
        "description": "'Correct_Response' is indeed correct, and can be derived from the context.",
        "labels": [
          { "name": "True 5️⃣", "key_value": "5" },
          { "name": "False 6️⃣", "key_value": "6" }
        ],
        "label_requirement": {
              "required": True
          }
    },
    {
        "annotation_type": "radio",
        "name": "s4_bresponse",
        "description": "'Correct_Response' is better than all of the 'Incorrect_Responses'.",
        "labels": [
          { "name": "True 7️⃣", "key_value": "7" },
          { "name": "False 8️⃣", "key_value": "8" }
        ],
        "label_requirement": {
              "required": True
          }
    }
  ],

    # The html that changes the visualiztation for your task. Change this file
    # to influence the layout and description of your task. This is not a full
    # HTML page, just the piece that does lays out your task's pieces
    #"html_layout": "templates/examples/fixed_keybinding_layout.html",
    "html_layout": "templates/layout.html",

    # The core UI files for Potato. You should not need to change these normally.
    #
    # Exceptions to this might include:
    # 1) You want to add custom CSS/fonts to style your task
    # 2) Your layout requires additional JS/assets to render
    # 3) You want to support additional keybinding magic
    #
    # if you want to use your own template,
    # please replace the string as a path to the template
    "base_html_template": "default",
    "header_file": "default",

    # This is where the actual HTML files will be generated
    "site_dir": "default"

}
